/**
 * Enhanced <PERSON><PERSON><PERSON> Handler for NestKo Application
 * Handles various types of errors including SMTP, validation, and server errors
 */

(function($) {
    'use strict';

    // Global error handler
    window.NestKoErrorHandler = {
        
        /**
         * Handle AJAX errors with proper error message extraction
         */
        handleAjaxError: function(xhr, status, error, customMessage) {
            let errorMessage = customMessage || 'An error occurred';
            
            try {
                if (xhr.responseJSON) {
                    // Handle JSON response errors
                    if (xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                } else if (xhr.responseText) {
                    // Handle text response errors
                    let responseText = xhr.responseText;
                    
                    // Check if it's an SMTP error
                    if (responseText.includes('SMTP') || responseText.includes('mail')) {
                        errorMessage = 'Email configuration error. Please check your mail settings.';
                    } else if (responseText.includes('Class') && responseText.includes('not found')) {
                        errorMessage = 'System error. Please contact administrator.';
                    } else {
                        // Try to extract meaningful error from HTML response
                        let tempDiv = document.createElement('div');
                        tempDiv.innerHTML = responseText;
                        let textContent = tempDiv.textContent || tempDiv.innerText || '';
                        
                        if (textContent.length > 200) {
                            errorMessage = 'Server error occurred. Please try again.';
                        } else {
                            errorMessage = textContent.substring(0, 200) + '...';
                        }
                    }
                }
            } catch (e) {
                console.error('Error parsing response:', e);
                errorMessage = 'An unexpected error occurred';
            }
            
            // Show user-friendly error message
            if (typeof toastr !== 'undefined') {
                toastr.error(errorMessage);
            } else {
                alert(errorMessage);
            }
            
            // Log detailed error for debugging
            console.error('AJAX Error Details:', {
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                error: error
            });
        },

        /**
         * Handle validation errors
         */
        handleValidationErrors: function(errors) {
            if (typeof errors === 'object') {
                Object.keys(errors).forEach(function(field) {
                    if (Array.isArray(errors[field])) {
                        errors[field].forEach(function(message) {
                            if (typeof toastr !== 'undefined') {
                                toastr.error(message);
                            }
                        });
                    }
                });
            }
        },

        /**
         * Safe jQuery each function that handles non-array-like objects
         */
        safeEach: function(obj, callback) {
            if (obj && typeof obj === 'object') {
                if (Array.isArray(obj) || obj.length !== undefined) {
                    // It's array-like, use jQuery each
                    $.each(obj, callback);
                } else {
                    // It's a plain object, iterate over properties
                    Object.keys(obj).forEach(function(key) {
                        callback(key, obj[key]);
                    });
                }
            }
        }
    };

    // Override jQuery's global error handler to use our enhanced handler
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        // Only handle if no specific error handler is defined
        if (!settings.error) {
            NestKoErrorHandler.handleAjaxError(xhr, 'error', thrownError);
        }
    });

    // Enhanced error function for forms
    window.handleFormError = function(xhr, status, error) {
        if (xhr.status === 422) {
            // Validation errors
            let response = xhr.responseJSON;
            if (response && response.errors) {
                NestKoErrorHandler.handleValidationErrors(response.errors);
            } else if (response && response.message) {
                toastr.error(response.message);
            }
        } else {
            NestKoErrorHandler.handleAjaxError(xhr, status, error);
        }
    };

})(jQuery);

// Ensure this script loads before other scripts that might use error handling
console.log('NestKo Error Handler loaded successfully');
