<?php

namespace App\Services\NestKo;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * NestKo Request Service
 * Handles HTTP requests for NestKo services
 */
class Request
{
    protected $timeout;
    protected $retries;
    protected $headers;

    public function __construct()
    {
        $this->timeout = getNestkoConfig('request.timeout', 30);
        $this->retries = getNestkoConfig('request.retries', 3);
        $this->headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'NestKo/1.0'
        ];
    }

    /**
     * Make a GET request
     */
    public function get(string $url, array $params = [], array $headers = []): array
    {
        return $this->makeRequest('GET', $url, $params, $headers);
    }

    /**
     * Make a POST request
     */
    public function post(string $url, array $data = [], array $headers = []): array
    {
        return $this->makeRequest('POST', $url, $data, $headers);
    }

    /**
     * Make a PUT request
     */
    public function put(string $url, array $data = [], array $headers = []): array
    {
        return $this->makeRequest('PUT', $url, $data, $headers);
    }

    /**
     * Make a DELETE request
     */
    public function delete(string $url, array $data = [], array $headers = []): array
    {
        return $this->makeRequest('DELETE', $url, $data, $headers);
    }

    /**
     * Make HTTP request with retry logic
     */
    protected function makeRequest(string $method, string $url, array $data = [], array $headers = []): array
    {
        $mergedHeaders = array_merge($this->headers, $headers);
        $attempt = 0;
        $lastException = null;

        while ($attempt < $this->retries) {
            try {
                $attempt++;
                
                $response = Http::withHeaders($mergedHeaders)
                    ->timeout($this->timeout)
                    ->retry($this->retries, 1000)
                    ->{strtolower($method)}($url, $data);

                if ($response->successful()) {
                    return [
                        'success' => true,
                        'data' => $response->json(),
                        'status' => $response->status(),
                        'headers' => $response->headers()
                    ];
                } else {
                    throw new \Exception("HTTP {$response->status()}: " . $response->body());
                }

            } catch (\Exception $e) {
                $lastException = $e;
                
                Log::warning("NestKo Request attempt {$attempt} failed", [
                    'method' => $method,
                    'url' => $url,
                    'error' => $e->getMessage(),
                    'attempt' => $attempt
                ]);

                if ($attempt >= $this->retries) {
                    break;
                }

                // Wait before retry (exponential backoff)
                sleep(pow(2, $attempt - 1));
            }
        }

        Log::error("NestKo Request failed after {$this->retries} attempts", [
            'method' => $method,
            'url' => $url,
            'error' => $lastException ? $lastException->getMessage() : 'Unknown error'
        ]);

        return [
            'success' => false,
            'error' => $lastException ? $lastException->getMessage() : 'Request failed',
            'status' => 500,
            'data' => null
        ];
    }

    /**
     * Set custom headers
     */
    public function withHeaders(array $headers): self
    {
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }

    /**
     * Set timeout
     */
    public function timeout(int $seconds): self
    {
        $this->timeout = $seconds;
        return $this;
    }

    /**
     * Set retry count
     */
    public function retries(int $count): self
    {
        $this->retries = $count;
        return $this;
    }
}
